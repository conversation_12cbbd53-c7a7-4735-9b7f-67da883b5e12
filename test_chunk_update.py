#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow API Key 测试脚本 - 块更新功能
测试Web界面和API key调用的差异
"""

import requests
import json
import sys

# 配置信息
BASE_URL = "http://192.168.66.13:9222"
API_KEY = "ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW"
DOC_ID = "fb633dee7e3311f0aa0b88f4da8e1b91"
CHUNK_ID = "e4cdd6d4421c3c2d"

# 需要从doc_id推断dataset_id，这里先设置为空，后续通过API获取
DATASET_ID = ""

def make_request(method, url, headers=None, data=None, json_data=None):
    """统一的请求处理函数"""
    try:
        print(f"\n🔗 {method.upper()} {url}")
        if headers:
            print(f"📋 Headers: {json.dumps(headers, indent=2)}")
        if json_data:
            print(f"📦 Request Body: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
        
        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            data=data,
            json=json_data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        try:
            result = response.json()
            print(f"📄 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return response.status_code, result
        except:
            print(f"📄 Response Text: {response.text}")
            return response.status_code, response.text
            
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return None, str(e)

def get_datasets():
    """获取数据集列表，找到包含指定文档的数据集"""
    print("\n" + "="*50)
    print("🔍 步骤1: 获取数据集列表")
    print("="*50)
    
    url = f"{BASE_URL}/api/v1/datasets"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    status_code, result = make_request("GET", url, headers=headers)
    
    if status_code == 200 and isinstance(result, dict) and result.get("code") == 0:
        datasets = result.get("data", [])
        print(f"✅ 成功获取 {len(datasets)} 个数据集")
        return datasets
    else:
        print(f"❌ 获取数据集失败")
        return []

def get_documents(dataset_id):
    """获取指定数据集中的文档列表"""
    print(f"\n🔍 检查数据集 {dataset_id} 中的文档")

    url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    status_code, result = make_request("GET", url, headers=headers)

    if status_code == 200 and isinstance(result, dict) and result.get("code") == 0:
        data = result.get("data", {})
        documents = data.get("docs", []) if isinstance(data, dict) else data
        print(f"✅ 数据集中有 {len(documents)} 个文档")

        # 查找目标文档
        for doc in documents:
            if isinstance(doc, dict) and doc.get("id") == DOC_ID:
                print(f"🎯 找到目标文档: {doc.get('name', 'Unknown')}")
                return True

        print(f"❌ 未找到目标文档 {DOC_ID}")
        return False
    else:
        print(f"❌ 获取文档列表失败")
        return False

def find_dataset_for_document():
    """查找包含指定文档的数据集"""
    datasets = get_datasets()
    
    for dataset in datasets:
        dataset_id = dataset.get("id")
        dataset_name = dataset.get("name", "Unknown")
        print(f"\n📁 检查数据集: {dataset_name} ({dataset_id})")
        
        if get_documents(dataset_id):
            print(f"🎯 找到目标数据集: {dataset_name}")
            return dataset_id
    
    print(f"❌ 未找到包含文档 {DOC_ID} 的数据集")
    return None

def get_chunk_content(dataset_id):
    """获取块的当前内容"""
    print("\n" + "="*50)
    print("🔍 步骤2: 获取块的当前内容")
    print("="*50)

    # 使用列出chunks的接口，通过id参数获取特定chunk
    url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{DOC_ID}/chunks"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    params = {
        "id": CHUNK_ID,
        "page": 1,
        "page_size": 1
    }

    status_code, result = make_request("GET", url + "?" + "&".join([f"{k}={v}" for k, v in params.items()]), headers=headers)

    if status_code == 200 and isinstance(result, dict) and result.get("code") == 0:
        data = result.get("data", {})
        chunks = data.get("chunks", []) if isinstance(data, dict) else []

        if chunks and len(chunks) > 0:
            chunk_data = chunks[0]
            content = chunk_data.get("content", "")
            print(f"✅ 成功获取块内容")
            print(f"📝 当前内容: {content}")
            return content
        else:
            print(f"❌ 未找到指定的块 {CHUNK_ID}")
            return None
    else:
        print(f"❌ 获取块内容失败")
        return None

def update_chunk_content(dataset_id, original_content):
    """更新块内容 - 在前面加上123"""
    print("\n" + "="*50)
    print("🔧 步骤3: 更新块内容")
    print("="*50)
    
    new_content = "123" + original_content
    print(f"📝 新内容: {new_content}")
    
    url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{DOC_ID}/chunks/{CHUNK_ID}"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "content": new_content,
        "important_keywords": []
    }
    
    status_code, result = make_request("PUT", url, headers=headers, json_data=data)
    
    if status_code == 200 and isinstance(result, dict) and result.get("code") == 0:
        print(f"✅ 块内容更新成功!")
        return True
    else:
        print(f"❌ 块内容更新失败")
        return False

def verify_update(dataset_id):
    """验证更新是否成功"""
    print("\n" + "="*50)
    print("🔍 步骤4: 验证更新结果")
    print("="*50)

    updated_content = get_chunk_content(dataset_id)

    if updated_content and updated_content.startswith("123"):
        print(f"✅ 验证成功! 内容已更新")
        return True
    else:
        print(f"❌ 验证失败! 内容未更新或更新不正确")
        print(f"📝 当前内容: {updated_content}")
        return False

def main():
    """主函数"""
    print("🚀 RAGFlow API Key 块更新测试")
    print(f"🔗 服务器: {BASE_URL}")
    print(f"🔑 API Key: {API_KEY}")
    print(f"📄 文档ID: {DOC_ID}")
    print(f"🧩 块ID: {CHUNK_ID}")
    
    # 步骤1: 查找数据集
    dataset_id = find_dataset_for_document()
    if not dataset_id:
        print("\n❌ 测试失败: 无法找到包含指定文档的数据集")
        sys.exit(1)
    
    print(f"\n🎯 使用数据集ID: {dataset_id}")
    
    # 步骤2: 获取原始内容
    original_content = get_chunk_content(dataset_id)
    if original_content is None:
        print("\n❌ 测试失败: 无法获取块的原始内容")
        sys.exit(1)
    
    # 步骤3: 更新内容
    if not update_chunk_content(dataset_id, original_content):
        print("\n❌ 测试失败: 块内容更新失败")
        sys.exit(1)
    
    # 步骤4: 验证更新
    if verify_update(dataset_id):
        print("\n🎉 测试成功! API Key 可以正常更新块内容")
    else:
        print("\n❌ 测试失败: 更新验证失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
