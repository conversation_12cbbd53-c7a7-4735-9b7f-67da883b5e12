<template>
  <div class="layout-padding">
    <el-card shadow="hover">
      <!-- 页面头部 -->
      <template #header>
        <div class="card-header">
          <div>
            <h2 style="margin: 0; display: flex; align-items: center; gap: 8px;">
              <el-icon><Collection /></el-icon>
              知识库管理
            </h2>
            <p style="margin: 8px 0 0 0; color: var(--el-text-color-regular); font-size: 14px;">
              管理您的AI知识库，支持创建、编辑、删除等操作
            </p>
          </div>
          <el-space>
            <!-- Token状态指示器 -->
            <el-tag :type="tokenStatusType" size="default">
              <el-icon><Connection /></el-icon>
              {{ tokenStatusText }}
            </el-tag>
            <el-button @click="testConnection" :loading="testLoading">
              测试连接
            </el-button>
            <el-button
              v-auth="'knowledge:base:create'"
              type="primary"
              @click="handleCreate"
              :icon="Plus"
            >
              创建知识库
            </el-button>
          </el-space>
        </div>
      </template>

      <!-- 统计卡片 -->
      <el-row :gutter="20" style="margin-bottom: 20px;" v-if="stats" v-auth="'knowledge:base:stats'">
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card>
            <el-statistic
              :value="stats.total_kb"
              title="知识库总数"
              :value-style="{ color: '#409EFF', fontSize: '24px', fontWeight: '600' }"
            >
              <template #prefix>
                <el-icon style="color: #409EFF; font-size: 20px;"><DataBoard /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card>
            <el-statistic
              :value="stats.total_documents"
              title="文档总数"
              :value-style="{ color: '#67C23A', fontSize: '24px', fontWeight: '600' }"
            >
              <template #prefix>
                <el-icon style="color: #67C23A; font-size: 20px;"><Document /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card>
            <el-statistic
              :value="stats.total_chunks"
              title="分块总数"
              :value-style="{ color: '#E6A23C', fontSize: '24px', fontWeight: '600' }"
            >
              <template #prefix>
                <el-icon style="color: #E6A23C; font-size: 20px;"><Grid /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card>
            <el-statistic
              :value="formatNumber(stats.total_tokens)"
              title="Token总数"
              :value-style="{ color: '#F56C6C', fontSize: '24px', fontWeight: '600' }"
            >
              <template #prefix>
                <el-icon style="color: #F56C6C; font-size: 20px;"><Coin /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索和操作栏 -->
      <el-card style="margin-bottom: 20px;">
        <el-row :gutter="20" justify="space-between" align="middle">
          <el-col :xs="24" :sm="16" :md="16" :lg="16">
            <el-space wrap>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索知识库名称"
                :prefix-icon="Search"
                clearable
                @input="handleSearch"
                style="width: 280px"
              />
              <el-select v-model="sortBy" @change="handleSortChange" style="width: 120px;" placeholder="排序方式">
                <el-option label="优先级" value="pagerank" />
                <el-option label="创建时间" value="create_time" />
                <el-option label="更新时间" value="update_time" />
              </el-select>
              <el-select v-model="queryParams.desc" @change="handleDescChange" style="width: 100px;" placeholder="排序">
                <el-option label="降序" :value="true" />
                <el-option label="升序" :value="false" />
              </el-select>
            </el-space>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8">
            <el-space style="width: 100%; justify-content: flex-end;">
              <el-button @click="loadKnowledgeBases" :icon="Refresh">刷新</el-button>
              <el-button
                v-auth="'knowledge:base:delete'"
                type="danger"
                @click="handleBatchDelete"
                :disabled="selectedKbs.length === 0"
                :icon="Delete"
              >
                批量删除
              </el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-card>

    <!-- 知识库列表 -->
    <div class="kb-list">
      <el-table
        v-loading="loading"
        :data="knowledgeBases"
        @selection-change="handleSelectionChange"
        border
        size="small"
        :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="知识库名称" width="200" align="center">
          <template #default="{ row }">
            <div class="kb-name-cell">
              <el-avatar :size="32" :src="row.avatar" class="kb-avatar">
                <el-icon><Collection /></el-icon>
              </el-avatar>
              <div class="kb-info">
                <div class="kb-name">{{ row.name }}</div>
                <div class="kb-description">{{ row.description || '暂无描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="embedding_model" label="嵌入模型" width="180" align="center">
          <template #default="{ row }">
            <el-tag size="small">{{ getModelDisplayName(row.embedding_model) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="chunk_method" label="分块方法" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ getChunkMethodName(row.chunk_method) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="文档" width="80" align="center">
          <template #default="{ row }">
            <span class="stat-number">{{ row.document_count }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分块" width="80" align="center">
          <template #default="{ row }">
            <span class="stat-number">{{ row.chunk_count }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Token" width="100" align="center">
          <template #default="{ row }">
            <span class="stat-number">{{ formatNumber(row.token_num) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="permission" label="权限" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.permission === 'me' ? 'warning' : 'success'" size="small">
              {{ row.permission === 'me' ? '私有' : '团队' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '1' ? 'success' : 'danger'" size="small">
              {{ row.status === '1' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_date" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.create_date) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="{ row }">
            <el-button
              v-auth="'knowledge:base:view'"
              size="small"
              type="primary"
              @click="handleView(row)"
            >查看</el-button>
            <el-button
              v-auth="'knowledge:base:update'"
              size="small"
              type="primary"
              @click="handleEdit(row)"
            >编辑</el-button>
            <el-button
              v-auth="'knowledge:base:delete'"
              size="small"
              type="danger"
              @click="handleDelete(row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-pagination
      v-show="total > 0"
      size="small"
      :total="total"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.page_size"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 30, 50]"
      :pager-count="5"
      background
      class="mt15"
      style="justify-content: flex-end;"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    </el-card>

    <!-- 创建/编辑知识库对话框 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="知识库名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入知识库名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入知识库描述"
        />
      </el-form-item>
      <!-- 嵌入模型、分块方法、访问权限使用后端默认值，不需要用户选择 -->
      <el-form-item label="优先级" prop="pagerank">
        <el-slider v-model="formData.pagerank" :min="0" :max="100" show-input />
        <div class="form-item-tip">数值越高优先级越高，影响搜索结果排序</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 查看知识库详情对话框 -->
  <el-dialog
    v-model="detailDialogVisible"
    title="知识库详情"
    width="800px"
  >
    <div v-if="currentKb" class="kb-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="知识库名称">{{ currentKb.name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentKb.status === '1' ? 'success' : 'danger'">
            {{ currentKb.status === '1' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ currentKb.description || '暂无描述' }}</el-descriptions-item>
        <el-descriptions-item label="嵌入模型">{{ getModelDisplayName(currentKb.embedding_model) }}</el-descriptions-item>
        <el-descriptions-item label="分块方法">{{ getChunkMethodName(currentKb.chunk_method) }}</el-descriptions-item>
        <el-descriptions-item label="访问权限">
          <el-tag :type="currentKb.permission === 'me' ? 'warning' : 'success'">
            {{ currentKb.permission === 'me' ? '私有' : '团队' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="页面排名">{{ currentKb.pagerank }}</el-descriptions-item>
        <el-descriptions-item label="文档数量">{{ currentKb.document_count }}</el-descriptions-item>
        <el-descriptions-item label="分块数量">{{ currentKb.chunk_count }}</el-descriptions-item>
        <el-descriptions-item label="Token数量">{{ formatNumber(currentKb.token_num) }}</el-descriptions-item>
        <el-descriptions-item label="相似度阈值">{{ currentKb.similarity_threshold }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(currentKb.create_date) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(currentKb.update_date) }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</div>
</template>

<script setup lang="ts" name="aiKbKbm">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { Session } from '/@/utils/storage';
import {
  Plus,
  Search,
  Refresh,
  Delete,
  Collection,
  DataBoard,
  Document,
  Grid,
  Coin,
  Connection
} from '@element-plus/icons-vue';
import {
  getKnowledgeBaseList,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBases,
  getKnowledgeBaseStats,
  getKnowledgeBaseDetail,
  checkKnowledgeBaseHealth,
  type KnowledgeBase,
  type KnowledgeBaseStats,
  type CreateKnowledgeBaseParams,
  type UpdateKnowledgeBaseParams,
  type KnowledgeBaseQueryParams
} from '/@/api/iot/knowledgeBase';

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const testLoading = ref(false);
const knowledgeBases = ref<KnowledgeBase[]>([]);
const selectedKbs = ref<KnowledgeBase[]>([]);
const stats = ref<KnowledgeBaseStats | null>(null);
const total = ref(0);
const searchKeyword = ref('');

// Token状态相关
const tokenStatus = ref<'checking' | 'connected' | 'disconnected' | 'error'>('checking');
const tokenStatusText = computed(() => {
  switch (tokenStatus.value) {
    case 'checking': return '检查连接中...';
    case 'connected': return '已连接';
    case 'disconnected': return '未连接';
    case 'error': return '连接错误';
    default: return '未知状态';
  }
});
const tokenStatusType = computed(() => {
  switch (tokenStatus.value) {
    case 'checking': return 'info';
    case 'connected': return 'success';
    case 'disconnected': return 'danger';
    case 'error': return 'warning';
    default: return 'info';
  }
});

// 对话框相关
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const isEdit = ref(false);
const currentKb = ref<KnowledgeBase | null>(null);

// 表单相关
const formRef = ref<FormInstance>();
const formData = reactive<CreateKnowledgeBaseParams>({
  name: '',
  description: '',
  pagerank: 0
  // 注意：其他字段使用后端默认值，不需要在前端初始化
});

// 查询参数
const queryParams = reactive<KnowledgeBaseQueryParams>({
  page: 1,
  page_size: 30,
  orderby: 'create_time',  // 默认按创建时间排序
  desc: true  // 降序排列，最新的在前面
});

// 排序相关
const sortBy = ref('pagerank');  // 前端排序字段，默认按优先级
const originalKnowledgeBases = ref<KnowledgeBase[]>([]);  // 原始数据

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 1, max: 128, message: '名称长度在 1 到 128 个字符', trigger: 'blur' }
  ]
  // 注意：嵌入模型、分块方法、访问权限使用后端默认值，不需要验证
};

// 注意：分块方法选项已移除，使用后端默认值

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑知识库' : '创建知识库');

// 生命周期
onMounted(() => {
  // 检查token状态
  checkTokenStatus();

  loadKnowledgeBases();
  loadStats();
  // 注意：不再需要加载选项，因为使用后端默认值
});

// 方法定义

/**
 * 检查token状态，确保知识库API能正常工作
 */
const checkTokenStatus = async () => {
  try {
    tokenStatus.value = 'checking';

    // 从主系统获取token
    const token = Session.get('token');

    if (!token) {
      console.warn('知识库页面：未找到有效token，请先登录主系统');
      tokenStatus.value = 'disconnected';
      ElMessage.warning('请先登录系统');
      return;
    }

    console.log('知识库页面：token状态正常', token.substring(0, 20) + '...');

    // 验证token有效性
    try {
      const response = await checkKnowledgeBaseHealth();
      const healthData = response.data;

      if (healthData.code === 200) {
        tokenStatus.value = 'connected';
      } else {
        console.warn('知识库服务连接异常:', healthData.msg);
        tokenStatus.value = 'error';
      }
    } catch (error: any) {
      console.error('知识库服务连接失败:', error);
      if (error.response?.status === 401) {
        tokenStatus.value = 'disconnected';
        ElMessage.error('登录已过期，请重新登录');
      } else {
        tokenStatus.value = 'error';
      }
    }

  } catch (error) {
    console.error('检查token状态失败:', error);
    tokenStatus.value = 'error';
  }
};

const loadKnowledgeBases = async () => {
  try {
    loading.value = true;
    const params = { ...queryParams };
    if (searchKeyword.value) {
      params.name = searchKeyword.value;
    }

    const response = await getKnowledgeBaseList(params);
    const businessData = response.data;

    if (businessData.code === 200 && businessData.data) {
      originalKnowledgeBases.value = businessData.data;
      total.value = businessData.data.length;
      // 应用前端排序
      applySorting();
    } else {
      ElMessage.error(businessData.msg || businessData.message || '获取知识库列表失败');
    }
  } catch (error) {
    console.error('加载知识库列表失败:', error);
    ElMessage.error('加载知识库列表失败');
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  try {
    const response = await getKnowledgeBaseStats();
    const businessData = response.data;
    if (businessData.code === 200 && businessData.data) {
      // 映射后端字段名到前端期望的字段名
      const rawStats = businessData.data;
      stats.value = {
        total_kb: rawStats.total_knowledge_bases,
        total_documents: rawStats.total_documents,
        total_chunks: rawStats.total_chunks,
        total_tokens: rawStats.total_tokens,
        active_kb: rawStats.total_knowledge_bases, // 暂时使用总数
        recent_created: 0, // 后端暂未提供
        storage_used: '未知', // 后端暂未提供
        last_update: new Date().toISOString() // 使用当前时间
      };
    }
  } catch (error) {
    console.error('加载统计信息失败:', error);
  }
};

// 注意：选项加载函数已移除，因为不再需要加载分块方法选项

const handleCreate = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

const handleEdit = (row: KnowledgeBase) => {
  isEdit.value = true;
  currentKb.value = row;

  // 填充表单数据 - 只填充允许编辑的字段
  Object.assign(formData, {
    name: row.name,
    description: row.description,
    pagerank: row.pagerank
    // 注意：embedding_model、permission、chunk_method不允许编辑
  });

  dialogVisible.value = true;
};

const handleView = async (row: KnowledgeBase) => {
  try {
    const response = await getKnowledgeBaseDetail(row.id!);
    const businessData = response.data;

    if (businessData.code === 200 && businessData.data) {
      currentKb.value = businessData.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage.error(businessData.msg || businessData.message || '获取知识库详情失败');
    }
  } catch (error) {
    console.error('获取知识库详情失败:', error);
    ElMessage.error('获取知识库详情失败');
  }
};

const handleDelete = async (row: KnowledgeBase) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除知识库 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await deleteKnowledgeBases([row.id!]);
    const businessData = response.data;
    if (businessData.code === 200) {
      ElMessage.success('删除成功');
      loadKnowledgeBases();
      loadStats();
    } else {
      ElMessage.error(businessData.msg || businessData.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除知识库失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const handleBatchDelete = async () => {
  if (selectedKbs.value.length === 0) {
    ElMessage.warning('请选择要删除的知识库');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedKbs.value.length} 个知识库吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const ids = selectedKbs.value.map(kb => kb.id!);
    const response = await deleteKnowledgeBases(ids);
    const businessData = response.data;
    if (businessData.code === 200) {
      ElMessage.success('批量删除成功');
      selectedKbs.value = [];
      loadKnowledgeBases();
      loadStats();
    } else {
      ElMessage.error(businessData.message || '批量删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    let response;
    if (isEdit.value && currentKb.value) {
      const updateData: UpdateKnowledgeBaseParams = {
        name: formData.name,
        description: formData.description,
        pagerank: formData.pagerank
      };
      // 注意：编辑时不允许修改embedding_model、permission、chunk_method
      // 这些字段使用创建时的值，避免影响已有数据
      response = await updateKnowledgeBase(currentKb.value.id!, updateData);
    } else {
      // 创建时，只传递用户填写的字段，其他使用后端默认值
      const createData: CreateKnowledgeBaseParams = {
        name: formData.name,
        description: formData.description,
        pagerank: formData.pagerank
        // 注意：embedding_model、permission、chunk_method使用后端默认值
        // parser_config也使用后端默认配置
      };
      response = await createKnowledgeBase(createData);
    }

    const businessData = response.data;
    if (businessData.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
      dialogVisible.value = false;
      loadKnowledgeBases();
      loadStats();
    } else {
      ElMessage.error(businessData.message || (isEdit.value ? '更新失败' : '创建失败'));
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('提交失败');
  } finally {
    submitting.value = false;
  }
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    name: '',
    description: '',
    pagerank: 0
    // 注意：其他字段使用后端默认值，不需要在前端设置
  });
  currentKb.value = null;
};

const handleSearch = () => {
  queryParams.page = 1;
  loadKnowledgeBases();
};

const handleSelectionChange = (selection: KnowledgeBase[]) => {
  selectedKbs.value = selection;
};

const handleSizeChange = (size: number) => {
  queryParams.page_size = size;
  queryParams.page = 1;
  loadKnowledgeBases();
};

const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  loadKnowledgeBases();
};

// 排序相关方法
const applySorting = () => {
  if (!originalKnowledgeBases.value.length) {
    knowledgeBases.value = [];
    return;
  }

  let sorted = [...originalKnowledgeBases.value];

  if (sortBy.value === 'pagerank') {
    // 按优先级排序（pagerank），支持升序/降序
    sorted.sort((a, b) => {
      const rankA = a.pagerank || 0;
      const rankB = b.pagerank || 0;
      return queryParams.desc ? rankB - rankA : rankA - rankB;
    });
  } else if (sortBy.value === 'create_time') {
    // 按创建时间排序，降序
    sorted.sort((a, b) => {
      const timeA = a.create_time || 0;
      const timeB = b.create_time || 0;
      return queryParams.desc ? timeB - timeA : timeA - timeB;
    });
  } else if (sortBy.value === 'update_time') {
    // 按更新时间排序，降序
    sorted.sort((a, b) => {
      const timeA = a.update_time || 0;
      const timeB = b.update_time || 0;
      return queryParams.desc ? timeB - timeA : timeA - timeB;
    });
  }

  knowledgeBases.value = sorted;
};

const handleSortChange = () => {
  if (sortBy.value === 'pagerank') {
    // 优先级排序不需要调用后端API
    applySorting();
  } else {
    // 时间排序需要调用后端API
    queryParams.orderby = sortBy.value as 'create_time' | 'update_time';
    loadKnowledgeBases();
  }
};

const handleDescChange = () => {
  if (sortBy.value === 'pagerank') {
    // 优先级排序时，只需要重新应用前端排序
    applySorting();
  } else {
    // 时间排序时，需要调用后端API
    loadKnowledgeBases();
  }
};

// 测试连接
const testConnection = async () => {
  testLoading.value = true;
  try {
    const response = await checkKnowledgeBaseHealth();
    const result = response.data;

    if (result.code === 200) {
      tokenStatus.value = 'connected';
      const message = result.data?.message || result.msg || '服务正常';
      ElMessage.success(`连接测试成功: ${message}`);
    } else {
      tokenStatus.value = 'error';
      const message = result.msg || result.message || '服务状态异常';
      ElMessage.warning(`连接测试警告: ${message}`);
    }
  } catch (error: any) {
    console.error('连接测试失败:', error);
    if (error.response?.status === 401) {
      tokenStatus.value = 'disconnected';
      ElMessage.error('认证失败，请重新登录');
    } else {
      tokenStatus.value = 'error';
      ElMessage.error('连接测试失败，请检查网络连接和服务配置');
    }
  } finally {
    testLoading.value = false;
  }
};

// 工具函数
const formatNumber = (num?: number): string => {
  if (!num || num === 0) return '0';
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatDate = (dateStr: string | undefined): string => {
  if (!dateStr) return '-';
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch {
    return dateStr;
  }
};

const getModelDisplayName = (model?: string): string => {
  if (!model) return '-';
  const parts = model.split('@');
  return parts[0] || model;
};

const getChunkMethodName = (method?: string): string => {
  if (!method) return '-';
  const methodMap: Record<string, string> = {
    naive: '通用',
    book: '书籍',
    email: '邮件',
    laws: '法律',
    manual: '手动',
    one: '单一',
    paper: '论文',
    picture: '图片',
    presentation: '演示文稿',
    qa: '问答',
    table: '表格',
    tag: '标签'
  };
  return methodMap[method] || method;
};
</script>

<style scoped>
/* ==========================================================================
   知识库管理页面样式 - 基于Element Plus + vue-next-admin
   ========================================================================== */

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

/* 知识库名称单元格 */
.kb-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.kb-info {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.kb-name {
  font-weight: 500;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.kb-description {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 统计数字样式 */
.stat-number {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

/* 统计信息单元格 */
.stats-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: var(--el-text-color-placeholder);
  margin-right: 4px;
  min-width: 30px;
}

.stat-value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

/* 表格样式优化 - 与通用物模型保持一致 */
:deep(.el-table) {
  /* 表格行高调整 - 更紧凑 */
  .el-table__row {
    height: 40px;
  }

  td {
    padding: 4px 0;
    text-align: center;
  }

  .cell {
    padding: 0 6px;
    line-height: 16px;
    font-size: 13px;
  }

  /* 操作列按钮样式 - 更小更紧凑 */
  .small-padding .cell {
    .el-button {
      margin-right: 4px;
      padding: 4px 8px;
      font-size: 12px;
      height: 24px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  /* 表头样式 */
  th {
    text-align: center;
    font-weight: 500;
    font-size: 13px;
    padding: 8px 0;
  }

  /* 标签样式调整 */
  .el-tag {
    font-size: 12px;
    height: 20px;
    line-height: 18px;
  }
}

:deep(.fixed-width) {
  width: 160px;
}

/* 分页样式调整 */
:deep(.el-pagination) {
  font-size: 13px;

  .el-pagination__total,
  .el-pagination__jump {
    font-size: 13px;
  }

  .el-pager li {
    font-size: 13px;
  }
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 15px;
  }

  .kb-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>