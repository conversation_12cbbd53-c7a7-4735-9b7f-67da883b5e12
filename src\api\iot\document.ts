// 文档管理相关接口

/**
 * 文档数据类型定义
 */
export interface DocumentInfo {
  id?: string;
  name: string;
  type?: string;
  size?: number;
  status?: 'uploading' | 'uploaded' | 'parsing' | 'parsed' | 'failed' | 'cancelled';
  chunk_num?: number;
  token_num?: number;
  parser_id?: string;
  parser_config?: any;
  thumbnail?: string;
  progress?: number;
  progress_msg?: string;
  create_time?: string;
  update_time?: string;
  created_by?: string;
  kb_id?: string; // 知识库ID
  dataset_id?: string; // RAGFlow数据集ID（与kb_id相同）
  // UI状态属性
  parsing?: boolean;
  stopping?: boolean;
  starting?: boolean;
  error_msg?: string;
}

/**
 * 文档上传请求参数
 */
export interface DocumentUploadParams {
  kb_id: string;
  parser_id?: string;
  run_after_upload?: boolean;
  upload_id?: string;
}

/**
 * 文档更新请求参数
 */
export interface DocumentUpdateParams {
  name?: string;
  parser_id?: string;
  parser_config?: any;
}

/**
 * 文档查询参数
 */
export interface DocumentQueryParams {
  kb_id: string;
  page?: number;
  page_size?: number;
  orderby?: string;
  desc?: boolean;
  keywords?: string;
  status?: string;
  type?: string;
}

/**
 * 文档删除请求参数
 */
export interface DocumentDeleteParams {
  kb_id: string;
  doc_ids: string[];
}

/**
 * 文档解析控制参数
 */
export interface DocumentParseParams {
  kb_id: string;
  doc_id: string;
  parser_id?: string;
  parser_config?: any;
}

/**
 * 上传进度信息
 */
export interface UploadProgress {
  upload_id: string;
  file_name: string;
  total_size: number;
  uploaded_size: number;
  progress: number;
  speed?: string;
  remaining_time?: string;
  status: string;
}

/**
 * 文档分块信息
 */
export interface DocumentChunk {
  id: string;
  content: string;
  content_ltks?: string;
  document_id: string;
  document_keyword?: string;
  highlight?: string;
  image_id?: string;
  important_keywords?: string[];
  kb_id: string;
  positions?: string[];
  similarity?: number;
  term_similarity?: number;
  vector_similarity?: number;
  token_count?: number; // Token数量
  create_time?: string;
  update_time?: string;
}

/**
 * 文档分块列表响应
 */
export interface DocumentChunksResponse {
  chunks: DocumentChunk[];
  doc: DocumentInfo;
  total: number;
}

/**
 * 文档分块查询参数
 */
export interface DocumentChunksQueryParams {
  dataset_id: string;
  document_id: string;
  keywords?: string;
  page?: number;
  page_size?: number;
  id?: string;
}

/**
 * 文档检索参数
 */
export interface DocumentRetrievalParams {
  question: string;
  dataset_ids?: string[];
  document_ids?: string[];
  page?: number;
  page_size?: number;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  top_k?: number;
  rerank_id?: string;
  keyword?: boolean;
  highlight?: boolean;
}

/**
 * 文档检索响应
 */
export interface DocumentRetrievalResponse {
  chunks: DocumentChunk[];
  doc_aggs: Array<{
    count: number;
    doc_id: string;
    doc_name: string;
  }>;
  total: number;
}

/**
 * API 响应格式
 */
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

// 创建专门用于FastAPI的axios实例（复用知识库的配置）
import { fastApiRequest } from '/@/api/iot/knowledgeBase';

/**
 * 上传文档到知识库
 */
export function uploadDocument(
  kbId: string, 
  file: File, 
  params: Omit<DocumentUploadParams, 'kb_id'> = {}
) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('parser_id', params.parser_id || 'naive');
  formData.append('run_after_upload', String(params.run_after_upload !== false));
  
  if (params.upload_id) {
    formData.append('upload_id', params.upload_id);
  }

  return fastApiRequest({
    url: `/api/iot/v1/documents/${kbId}/upload`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 360000, // 6分钟超时，确保大文件上传有足够时间（后端设置为2分钟*3=6分钟）
    onUploadProgress: (progressEvent) => {
      // 上传进度回调可以在这里处理
      if (progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`上传进度: ${progress}%`);
      }
    }
  });
}

/**
 * 获取文档列表
 */
export function getDocumentList(params: DocumentQueryParams) {
  const { kb_id, ...queryParams } = params;
  return fastApiRequest({
    url: `/api/iot/v1/documents/${kb_id}/list`,
    method: 'get',
    params: queryParams
  });
}



/**
 * 更新文档信息
 */
export function updateDocument(kbId: string, docId: string, data: DocumentUpdateParams) {
  return fastApiRequest({
    url: `/api/iot/v1/documents/${kbId}/${docId}`,
    method: 'put',
    data
  });
}

/**
 * 删除文档
 */
export function deleteDocuments(params: DocumentDeleteParams) {
  const { kb_id, doc_ids } = params;
  return fastApiRequest({
    url: `/api/iot/v1/documents/${kb_id}/delete`,
    method: 'delete',
    data: { doc_ids }
  });
}

/**
 * 开始文档解析
 */
export function startDocumentParsing(params: DocumentParseParams) {
  const { kb_id, doc_id, ...parseData } = params;
  return fastApiRequest({
    url: `/api/iot/v1/documents/${kb_id}/${doc_id}/parse`,
    method: 'post',
    data: parseData
  });
}

/**
 * 停止文档解析
 */
export function stopDocumentParsing(kbId: string, docId: string) {
  return fastApiRequest({
    url: `/api/iot/v1/documents/${kbId}/${docId}/parse`,
    method: 'delete'
  });
}



/**
 * 查询上传进度
 */
export function getUploadProgress(uploadId: string) {
  return fastApiRequest({
    url: `/api/iot/v1/documents/upload-progress/${uploadId}`,
    method: 'get'
  });
}

/**
 * 检查文档上传状态（用于超时后的状态确认）
 */
export function checkDocumentUploadStatus(kbId: string, fileName: string) {
  return fastApiRequest({
    url: `/api/iot/v1/documents/${kbId}/check-upload-status`,
    method: 'get',
    params: { fileName }
  });
}

/**
 * 获取文档的分块列表
 */
export function getDocumentChunks(params: DocumentChunksQueryParams): Promise<ApiResponse<DocumentChunksResponse>> {
  const { dataset_id, document_id, ...queryParams } = params;
  return fastApiRequest({
    url: `/api/iot/v1/documents/${dataset_id}/${document_id}/chunks`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 检索文档分块（带搜索功能）
 */
export function retrieveDocumentChunks(params: DocumentRetrievalParams): Promise<ApiResponse<DocumentRetrievalResponse>> {
  return fastApiRequest({
    url: `/api/iot/v1/documents/retrieval`,
    method: 'post',
    data: params
  });
}

/**
 * 获取单个文档的解析结果统计信息
 */
export function getDocumentParseResult(datasetId: string, documentId: string) {
  return fastApiRequest({
    url: `/api/iot/v1/documents/${datasetId}/${documentId}/parse-result`,
    method: 'get'
  });
}

// ==================== 文档分块CRUD操作API ====================

/**
 * 文档分块创建请求参数
 */
export interface DocumentChunkCreateRequest {
  content: string;
  important_keywords?: string[];
  questions?: string[];
}

/**
 * 文档分块更新请求参数
 */
export interface DocumentChunkUpdateRequest {
  content?: string;
  important_keywords?: string[];
  available?: boolean;
}

/**
 * 文档分块删除请求参数
 */
export interface DocumentChunkDeleteRequest {
  chunk_ids: string[];
}

/**
 * 创建文档分块
 * 根据RAGFlow API规范：POST /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
 */
export function createDocumentChunk(
  datasetId: string,
  documentId: string,
  chunkData: DocumentChunkCreateRequest
): Promise<ApiResponse<any>> {
  return fastApiRequest({
    url: `/api/iot/v1/datasets/${datasetId}/documents/${documentId}/chunks`,
    method: 'post',
    data: chunkData
  });
}

/**
 * 更新文档分块
 * 根据RAGFlow API规范：PUT /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}
 */
export function updateDocumentChunk(
  datasetId: string,
  documentId: string,
  chunkId: string,
  chunkData: DocumentChunkUpdateRequest
): Promise<ApiResponse<any>> {
  return fastApiRequest({
    url: `/api/iot/v1/datasets/${datasetId}/documents/${documentId}/chunks/${chunkId}`,
    method: 'put',
    data: chunkData
  });
}

/**
 * 删除文档分块
 * 根据RAGFlow API规范：DELETE /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
 */
export function deleteDocumentChunks(
  datasetId: string,
  documentId: string,
  deleteData: DocumentChunkDeleteRequest
): Promise<ApiResponse<any>> {
  return fastApiRequest({
    url: `/api/iot/v1/datasets/${datasetId}/documents/${documentId}/chunks`,
    method: 'delete',
    data: deleteData
  });
}

/**
 * 批量操作文档分块
 * 注意：RAGFlow API暂不支持批量操作接口，此函数保留用于未来扩展
 * 当前建议使用单个操作API进行逐一处理
 */
export function batchOperateDocumentChunks(
  _datasetId: string,
  _documentId: string,
  _operations: Array<{
    operation: 'create' | 'update' | 'delete';
    chunk_id?: string;
    data: any;
  }>
): Promise<ApiResponse<any>> {
  // 暂时返回错误，提示使用单个操作API
  return Promise.reject(new Error('批量操作暂不支持，请使用单个操作API'));

  // 保留原始实现用于未来
  // return fastApiRequest({
  //   url: `/api/iot/v1/datasets/${datasetId}/documents/${documentId}/chunks/batch`,
  //   method: 'post',
  //   data: { operations }
  // });
}

/**
 * 批量上传文档
 */
export async function batchUploadDocuments(
  kbId: string, 
  files: File[], 
  params: Omit<DocumentUploadParams, 'kb_id'> = {},
  onProgress?: (progress: { completed: number; total: number; currentFile?: string }) => void
) {
  const results = [];
  const total = files.length;
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    try {
      // 通知当前上传进度
      if (onProgress) {
        onProgress({
          completed: i,
          total,
          currentFile: file.name
        });
      }
      
      const result = await uploadDocument(kbId, file, params);
      results.push({
        file: file.name,
        success: true,
        result
      });
      
    } catch (error) {
      results.push({
        file: file.name,
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      });
    }
  }
  
  // 通知完成
  if (onProgress) {
    onProgress({
      completed: total,
      total
    });
  }
  
  return results;
}

/**
 * 获取支持的文件类型
 */
export function getSupportedFileTypes() {
  return [
    { label: 'PDF文档', value: 'pdf', accept: '.pdf', mime: 'application/pdf' },
    { label: 'Word文档', value: 'docx', accept: '.docx,.doc', mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword' },
    { label: 'Excel表格', value: 'xlsx', accept: '.xlsx,.xls', mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel' },
    { label: 'PowerPoint演示', value: 'pptx', accept: '.pptx,.ppt', mime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-powerpoint' },
    { label: '文本文件', value: 'txt', accept: '.txt', mime: 'text/plain' },
    { label: 'Markdown', value: 'md', accept: '.md', mime: 'text/markdown' },
    { label: 'HTML文件', value: 'html', accept: '.html,.htm', mime: 'text/html' },
    { label: 'CSV文件', value: 'csv', accept: '.csv', mime: 'text/csv' },
    { label: 'JSON文件', value: 'json', accept: '.json', mime: 'application/json' },
    { label: 'XML文件', value: 'xml', accept: '.xml', mime: 'application/xml,text/xml' }
  ];
}

/**
 * 获取解析器选项
 */
export function getParserOptions() {
  return [
    { label: '通用解析器', value: 'naive', description: '适用于大多数文档类型' },
    { label: '书籍解析器', value: 'book', description: '适用于书籍、长篇文档' },
    { label: '邮件解析器', value: 'email', description: '适用于邮件格式文档' },
    { label: '法律解析器', value: 'laws', description: '适用于法律文档' },
    { label: '手动解析器', value: 'manual', description: '手动配置解析参数' },
    { label: '论文解析器', value: 'paper', description: '适用于学术论文' },
    { label: '图片解析器', value: 'picture', description: '适用于图片文档' },
    { label: '演示文稿解析器', value: 'presentation', description: '适用于PPT等演示文稿' },
    { label: '问答解析器', value: 'qa', description: '适用于问答格式文档' },
    { label: '表格解析器', value: 'table', description: '适用于表格数据' },
    { label: '标签解析器', value: 'tag', description: '适用于标签化文档' }
  ];
}

/**
 * 验证文件类型
 */
export function validateFileType(file: File): boolean {
  const supportedTypes = getSupportedFileTypes();
  const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
  
  return supportedTypes.some(type => 
    type.accept.split(',').some(accept => accept.trim() === fileExt)
  );
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件类型图标
 */
export function getFileTypeIcon(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase();
  const iconMap: Record<string, string> = {
    pdf: 'document-pdf',
    doc: 'document-word',
    docx: 'document-word',
    xls: 'document-excel',
    xlsx: 'document-excel',
    ppt: 'document-powerpoint',
    pptx: 'document-powerpoint',
    txt: 'document-text',
    md: 'document-markdown',
    html: 'document-html',
    csv: 'document-csv',
    json: 'document-json',
    xml: 'document-xml'
  };
  return iconMap[ext || ''] || 'document';
}
