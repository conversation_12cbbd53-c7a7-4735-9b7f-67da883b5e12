# 文档块操作功能测试指南

## 📋 修改内容概述

根据RAGFlow API文档的限制，已对前端界面的块操作功能进行了以下修改：

### ✅ 已完成的修改

1. **移除了"在指定位置插入块"功能**
   - 删除了分块列表中间的"在此处插入分块"按钮
   - 移除了插入位置参数相关代码

2. **保留并优化了现有功能**
   - ✅ **添加块**：支持在文档末尾添加新分块
   - ✅ **修改块内容**：支持编辑现有分块的内容和关键词
   - ✅ **删除块**：支持单个删除和批量删除分块

3. **更新了用户界面提示**
   - 添加了API限制说明
   - 提供了替代方案提示（先删除再添加）
   - 优化了对话框和操作说明

4. **修正了API接口**
   - 更新API URL以匹配RAGFlow标准格式
   - 添加了API规范注释
   - 禁用了不支持的批量操作接口

## 🔧 支持的API操作

### 1. 添加分块
```
POST /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
```
- ✅ 支持：在文档末尾添加新分块
- ❌ 不支持：在指定位置插入分块

### 2. 更新分块
```
PUT /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}
```
- ✅ 支持：修改分块内容、关键词、可用性状态

### 3. 删除分块
```
DELETE /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
```
- ✅ 支持：删除指定ID的分块
- ✅ 支持：批量删除多个分块

## 🧪 测试步骤

### 测试1：添加新分块
1. 进入知识库文件管理页面
2. 选择一个已解析的文档
3. 点击"添加新分块"按钮
4. 输入分块内容和关键词
5. 确认分块被添加到文档末尾

### 测试2：修改分块内容
1. 在分块列表中点击"编辑"按钮
2. 修改分块内容
3. 点击"保存"确认修改
4. 验证内容已更新

### 测试3：删除分块
1. 单个删除：点击分块的"删除"按钮
2. 批量删除：
   - 点击"批量选择"
   - 选中多个分块
   - 点击"删除选中"

### 测试4：验证API限制提示
1. 查看分块列表顶部的操作说明
2. 确认添加分块对话框中的API限制说明
3. 验证不再有"插入"相关的按钮

## 🚨 注意事项

1. **位置调整替代方案**
   - 如需调整分块顺序，需要先删除相关分块
   - 然后按正确顺序重新添加分块

2. **API兼容性**
   - 所有API调用都符合RAGFlow OpenAPI规范
   - 移除了不支持的批量操作接口

3. **用户体验**
   - 添加了清晰的操作限制说明
   - 提供了替代方案指导
   - 保持了现有功能的完整性

## 📁 修改的文件

1. `src/components/FileManagement/DocumentParseStatus.vue`
   - 移除插入位置功能
   - 添加操作说明和限制提示
   - 优化添加分块对话框

2. `src/api/iot/document.ts`
   - 更新API URL格式
   - 添加RAGFlow API规范注释
   - 禁用不支持的批量操作

## ✅ 验收标准

- [ ] 不再显示"在指定位置插入分块"按钮
- [ ] 添加分块功能正常工作（添加到末尾）
- [ ] 修改分块内容功能正常工作
- [ ] 删除分块功能正常工作（单个和批量）
- [ ] 显示清晰的API限制说明
- [ ] 提供替代方案指导
- [ ] API调用符合RAGFlow规范

---

*测试完成后，请确认所有功能都按预期工作，并且用户界面清晰地传达了API限制信息。*
