# Git未暂存修改文件汇总

## 🔧 仓库1: fastapi_best_architecture (FastAPI后端)

**分支**: `dev-wjj`

### 🔄 Changes not staged for commit (未暂存的修改) - 12个文件
```
CRITICAL_INFO_AND_TASKS.md
ragflow_openapi.json
RAGFlow集成项目综合指南.md
backend/app/iot/api/v1/document.py
backend/app/iot/api/v1/router.py
backend/app/iot/schema/document.py
backend/app/iot/service/document_service.py
backend/app/iot/service/knowledge_base_service.py
backend/app/iot/utils/file_upload.py
backend/core/conf.py
backend/middleware/opera_log_middleware.py
scripts/permission_testing/check_thingsmodel_permissions.py
```

---

## 🎨 仓库2: TS-IOT-SYS-WEBUI (前端UI)

**分支**: `Dev-Wjj`

### 🔄 Changes not staged for commit (未暂存的修改) - 11个文件
```
git_status_summary.md
package-lock.json
package.json
src/api/iot/document.ts
src/api/iot/knowledgeBase.ts
src/components/FileManagement/DocumentListVue
src/components/FileManagement/DocumentParseStatus.vue
src/components/FileManagement/DocumentPreview.vue
src/components/FileManagement/VueOfficePreview.vue
src/components/FileUpload/DocumentUpload.vue
src/views/ai/kb/fm/index.vue
```

---

## � 文件详细列表

### FastAPI后端仓库修改文件 (12个):
1. `CRITICAL_INFO_AND_TASKS.md` - 关键信息和任务文档
2. `ragflow_openapi.json` - RAGFlow OpenAPI规范文件
3. `RAGFlow集成项目综合指南.md` - RAGFlow集成指南
4. `backend/app/iot/api/v1/document.py` - 文档管理API
5. `backend/app/iot/api/v1/router.py` - IoT API路由配置
6. `backend/app/iot/schema/document.py` - 文档数据模型
7. `backend/app/iot/service/document_service.py` - 文档服务逻辑
8. `backend/app/iot/service/knowledge_base_service.py` - 知识库服务
9. `backend/app/iot/utils/file_upload.py` - 文件上传工具
10. `backend/core/conf.py` - 核心配置文件
11. `backend/middleware/opera_log_middleware.py` - 操作日志中间件
12. `scripts/permission_testing/check_thingsmodel_permissions.py` - 权限测试脚本

### 前端UI仓库修改文件 (11个):
1. `git_status_summary.md` - Git状态汇总文档
2. `package-lock.json` - 依赖锁定文件
3. `package.json` - 项目配置文件
4. `src/api/iot/document.ts` - 文档API接口
5. `src/api/iot/knowledgeBase.ts` - 知识库API接口
6. `src/components/FileManagement/DocumentListVue` - 文档列表组件
7. `src/components/FileManagement/DocumentParseStatus.vue` - 文档解析状态组件
8. `src/components/FileManagement/DocumentPreview.vue` - 文档预览组件
9. `src/components/FileManagement/VueOfficePreview.vue` - Office文档预览组件
10. `src/components/FileUpload/DocumentUpload.vue` - 文档上传组件
11. `src/views/ai/kb/fm/index.vue` - 知识库文件管理页面

---

*报告生成时间: 2025-08-22*
